
> @mongodb-ai/core@0.1.0 build
> tsc -p tsconfig.build.json

[1G[0K[96msrc/persistance/MongoDataStore.ts[0m:[93m17[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'WithId<T> | null' is not assignable to type 'T | null'.
  Type 'WithId<T>' is not assignable to type 'T | null'.
    Type 'WithId<T>' is not assignable to type 'T'.
      'WithId<T>' is assignable to the constraint of type 'T', but 'T' could be instantiated with a different subtype of constraint '{ _id?: ObjectId | undefined; }'.

[7m17[0m     return this.collection.findOne({ _id: new ObjectId(id) } as any);
[7m  [0m [91m    ~~~~~~[0m

[96msrc/persistance/MongoDataStore.ts[0m:[93m21[0m:[93m5[0m - [91merror[0m[90m TS2322: [0mType 'WithId<T>[]' is not assignable to type 'T[]'.
  Type 'WithId<T>' is not assignable to type 'T'.
    'WithId<T>' is assignable to the constraint of type 'T', but 'T' could be instantiated with a different subtype of constraint '{ _id?: ObjectId | undefined; }'.

[7m21[0m     return this.collection.find(filter as any).toArray();
[7m  [0m [91m    ~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m1[0m:[93m25[0m - [91merror[0m[90m TS2732: [0mCannot find module './agent.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m1[0m import agentSchema from './agent.schema.json';
[7m [0m [91m                        ~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m2[0m:[93m31[0m - [91merror[0m[90m TS2732: [0mCannot find module './agent_memory.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m2[0m import agentMemorySchema from './agent_memory.schema.json';
[7m [0m [91m                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m3[0m:[93m34[0m - [91merror[0m[90m TS2732: [0mCannot find module './agent_workflows.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m3[0m import agentWorkflowsSchema from './agent_workflows.schema.json';
[7m [0m [91m                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m4[0m:[93m36[0m - [91merror[0m[90m TS2732: [0mCannot find module './vector_embeddings.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m4[0m import vectorEmbeddingsSchema from './vector_embeddings.schema.json';
[7m [0m [91m                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m5[0m:[93m34[0m - [91merror[0m[90m TS2732: [0mCannot find module './tool_executions.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m5[0m import toolExecutionsSchema from './tool_executions.schema.json';
[7m [0m [91m                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m6[0m:[93m26[0m - [91merror[0m[90m TS2732: [0mCannot find module './traces.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m6[0m import tracesSchema from './traces.schema.json';
[7m [0m [91m                         ~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m7[0m:[93m32[0m - [91merror[0m[90m TS2732: [0mCannot find module './dynamic_plans.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m7[0m import dynamicPlansSchema from './dynamic_plans.schema.json';
[7m [0m [91m                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m8[0m:[93m31[0m - [91merror[0m[90m TS2732: [0mCannot find module './evaluations.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m8[0m import evaluationsSchema from './evaluations.schema.json';
[7m [0m [91m                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m9[0m:[93m33[0m - [91merror[0m[90m TS2732: [0mCannot find module './human_feedback.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m9[0m import humanFeedbackSchema from './human_feedback.schema.json';
[7m [0m [91m                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m10[0m:[93m36[0m - [91merror[0m[90m TS2732: [0mCannot find module './agent_permissions.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m10[0m import agentPermissionsSchema from './agent_permissions.schema.json';
[7m  [0m [91m                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m11[0m:[93m36[0m - [91merror[0m[90m TS2732: [0mCannot find module './resource_registry.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m11[0m import resourceRegistrySchema from './resource_registry.schema.json';
[7m  [0m [91m                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m12[0m:[93m37[0m - [91merror[0m[90m TS2732: [0mCannot find module './secure_credentials.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m12[0m import secureCredentialsSchema from './secure_credentials.schema.json';
[7m  [0m [91m                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m13[0m:[93m38[0m - [91merror[0m[90m TS2732: [0mCannot find module './ingestion_pipelines.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m13[0m import ingestionPipelinesSchema from './ingestion_pipelines.schema.json';
[7m  [0m [91m                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/schemas/index.ts[0m:[93m14[0m:[93m31[0m - [91merror[0m[90m TS2732: [0mCannot find module './agent_events.schema.json'. Consider using '--resolveJsonModule' to import module with '.json' extension.

[7m14[0m import agentEventsSchema from './agent_events.schema.json';
[7m  [0m [91m                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m


Found 16 errors in 2 files.

Errors  Files
     2  src/persistance/MongoDataStore.ts[90m:17[0m
    14  src/schemas/index.ts[90m:1[0m
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m Lifecycle script `build` failed with error:
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mcode[39m [33m2[39m
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mpath[39m /Users/<USER>/Desktop/MongoDB AI Agent Boilerplate/packages/core
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mworkspace[39m @mongodb-ai/core@0.1.0
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mlocation[39m /Users/<USER>/Desktop/MongoDB AI Agent Boilerplate/packages/core
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m command failed
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mcommand[39m sh -c tsc -p tsconfig.build.json
[1G[0K⠙[1G[0K

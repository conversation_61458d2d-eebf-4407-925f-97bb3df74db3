import { Db, Document } from 'mongodb';
export type ChangeHandler = (change: Document) => Promise<void>;
export declare class ChangeStreamManager {
    private changeStream?;
    private db;
    private collectionName;
    private pipeline;
    private handler;
    constructor(db: Db, collectionName: string, pipeline: Document[], handler: ChangeHandler);
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=ChangeStreamManager.d.ts.map
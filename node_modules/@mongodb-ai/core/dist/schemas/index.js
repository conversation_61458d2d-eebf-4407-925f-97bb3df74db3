"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.schemas = void 0;
var agent_schema_json_1 = __importDefault(require("./agent.schema.json"));
var agent_memory_schema_json_1 = __importDefault(require("./agent_memory.schema.json"));
var agent_workflows_schema_json_1 = __importDefault(require("./agent_workflows.schema.json"));
var vector_embeddings_schema_json_1 = __importDefault(require("./vector_embeddings.schema.json"));
var tool_executions_schema_json_1 = __importDefault(require("./tool_executions.schema.json"));
var traces_schema_json_1 = __importDefault(require("./traces.schema.json"));
var dynamic_plans_schema_json_1 = __importDefault(require("./dynamic_plans.schema.json"));
var evaluations_schema_json_1 = __importDefault(require("./evaluations.schema.json"));
var human_feedback_schema_json_1 = __importDefault(require("./human_feedback.schema.json"));
var agent_permissions_schema_json_1 = __importDefault(require("./agent_permissions.schema.json"));
var resource_registry_schema_json_1 = __importDefault(require("./resource_registry.schema.json"));
var secure_credentials_schema_json_1 = __importDefault(require("./secure_credentials.schema.json"));
var ingestion_pipelines_schema_json_1 = __importDefault(require("./ingestion_pipelines.schema.json"));
var agent_events_schema_json_1 = __importDefault(require("./agent_events.schema.json"));
exports.schemas = {
    agent: agent_schema_json_1.default,
    agentMemory: agent_memory_schema_json_1.default,
    agentWorkflows: agent_workflows_schema_json_1.default,
    vectorEmbeddings: vector_embeddings_schema_json_1.default,
    toolExecutions: tool_executions_schema_json_1.default,
    traces: traces_schema_json_1.default,
    dynamicPlans: dynamic_plans_schema_json_1.default,
    evaluations: evaluations_schema_json_1.default,
    humanFeedback: human_feedback_schema_json_1.default,
    agentPermissions: agent_permissions_schema_json_1.default,
    resourceRegistry: resource_registry_schema_json_1.default,
    secureCredentials: secure_credentials_schema_json_1.default,
    ingestionPipelines: ingestion_pipelines_schema_json_1.default,
    agentEvents: agent_events_schema_json_1.default,
};

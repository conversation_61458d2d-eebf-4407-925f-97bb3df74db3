import { Db } from 'mongodb';
import { IEmbeddingStore, Vector, QueryFilter } from './IEmbeddingStore';
export declare class MongoEmbeddingProvider implements IEmbeddingStore {
    private collection;
    constructor(db: Db, collectionName: string);
    upsert(vectors: Vector[]): Promise<void>;
    query(vector: number[], topK: number, filter?: QueryFilter): Promise<Vector[]>;
}
//# sourceMappingURL=MongoEmbeddingProvider.d.ts.map
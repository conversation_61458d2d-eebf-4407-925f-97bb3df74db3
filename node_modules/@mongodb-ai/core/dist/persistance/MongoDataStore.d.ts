import { Db, ObjectId } from 'mongodb';
import { IDataStore } from './IDataStore';
export declare class MongoDataStore<T extends {
    _id?: ObjectId;
}> implements IDataStore<T> {
    private collection;
    constructor(db: Db, collectionName: string);
    create(item: T): Promise<T>;
    findById(id: string): Promise<T | null>;
    find(filter: Partial<T>): Promise<T[]>;
    update(id: string, item: Partial<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
}
//# sourceMappingURL=MongoDataStore.d.ts.map
import { IDataStore } from '../persistance/IDataStore';
export interface AgentState {
    _id: any;
    agent_id: string;
    name: string;
    status: 'active' | 'inactive';
    model_config: {
        provider: string;
        model: string;
        system_prompt: string;
    };
    tools: {
        tool_id: string;
        name: string;
    }[];
}
export declare class AgentStateManager {
    private dataStore;
    private agentState;
    constructor(dataStore: IDataStore<AgentState>);
    loadState(agentId: string): Promise<void>;
    getState(): AgentState;
    getSystemPrompt(): string;
    getTools(): {
        tool_id: string;
        name: string;
    }[];
}
//# sourceMappingURL=AgentStateManager.d.ts.map
import { IDataStore } from '../persistance/IDataStore';
import { AgentStateManager } from './AgentStateManager';
import { ToolExecutor } from './ToolExecutor';
interface Workflow {
    _id: any;
    workflow_id: string;
    status: string;
    workflow_definition: {
        steps: {
            step_id: string;
            agent_id: string;
            description: string;
            depends_on?: string[];
            tool_id: string;
            input_mapping: any;
        }[];
    };
    execution_log: any[];
    shared_context: any;
}
export declare class WorkflowEngine {
    private workflowStore;
    private agentStateManager;
    private toolExecutor;
    constructor(workflowStore: IDataStore<Workflow>, agentStateManager: AgentStateManager, toolExecutor: ToolExecutor);
    executeWorkflow(workflowId: string): Promise<void>;
    private updateWorkflowStatus;
    private updateWorkflowContext;
}
export {};
//# sourceMappingURL=WorkflowEngine.d.ts.map
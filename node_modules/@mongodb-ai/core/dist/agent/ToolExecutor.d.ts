import { IDataStore } from '../persistance/IDataStore';
interface ToolExecutionLog {
    tool_id: string;
    input: any;
    output: any;
    execution_time_ms: number;
    success: boolean;
    error?: {
        message: string;
        stack?: string;
    };
}
export declare class ToolExecutor {
    private logStore;
    constructor(logStore: IDataStore<ToolExecutionLog>);
    execute(toolId: string, input: any): Promise<any>;
}
export {};
//# sourceMappingURL=ToolExecutor.d.ts.map
{"name": "mongodb-ai-agent-boilerplate", "version": "0.1.0", "private": true, "description": "The world's #1 AI Agent Boilerplate, built on MongoDB.", "author": "MongoDB AI", "license": "Apache-2.0", "workspaces": ["packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "test": "turbo run test", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/uuid": "^9.0.8", "eslint": "^8.57.0", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "turbo": "^1.12.4", "typescript": "^5.3.3"}, "dependencies": {"dotenv": "^16.4.5", "uuid": "^9.0.1"}, "packageManager": "npm@10.2.4"}
2025-06-21T13:43:42.277118Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie")}
2025-06-21T13:43:42.277130Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-21T13:43:42.377271Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules/.cache"), AnchoredSystemPathBuf(".turbo/cookies/2.cookie"), AnchoredSystemPathBuf(".turbo/cookies/3.cookie"), AnchoredSystemPathBuf("packages/utils/.turbo/turbo-build.log"), AnchoredSystemPathBuf("packages/core/.turbo"), AnchoredSystem<PERSON><PERSON><PERSON>uf("node_modules/.cache/turbo"), AnchoredSystemPathBuf(".turbo/cookies/1.cookie"), AnchoredSystemPathBuf("packages/core/.turbo/turbo-build.log"), AnchoredSystemPathBuf("packages/utils/.turbo")}
2025-06-21T13:43:42.377284Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@mongodb-ai/utils"), path: AnchoredSystemPathBuf("packages/utils") }}))
2025-06-21T13:43:43.277794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/utils/dist/logger.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/persistance"), AnchoredSystemPathBuf("packages/core/dist/persistance/IDataStore.js"), AnchoredSystemPathBuf("packages/utils/dist/index.d.ts"), AnchoredSystemPathBuf("node_modules/.cache/turbo/7af278aea0d7bab5-meta.json"), AnchoredSystemPathBuf("node_modules/.cache/turbo/7af278aea0d7bab5.tar.zst"), AnchoredSystemPathBuf("packages/utils/dist/index.js"), AnchoredSystemPathBuf("packages/utils/.turbo/turbo-build.log"), AnchoredSystemPathBuf("packages/utils/dist"), AnchoredSystemPathBuf("packages/utils/dist/logger.d.ts"), AnchoredSystemPathBuf("packages/utils/dist/index.d.ts.map"), AnchoredSystemPathBuf(".turbo/cookies/4.cookie"), AnchoredSystemPathBuf("packages/core/dist"), AnchoredSystemPathBuf("packages/utils/dist/logger.js")}
2025-06-21T13:43:43.277834Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/utils"), path: AnchoredSystemPathBuf("packages/utils") }, WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-21T13:43:43.378020Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/.turbo/turbo-build.log"), AnchoredSystemPathBuf("packages/core/dist/persistance/IDataStore.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/persistance/IEmbeddingStore.js"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoConnection.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/agent/AgentStateManager.js"), AnchoredSystemPathBuf("packages/core/dist/agent/index.d.ts"), AnchoredSystemPathBuf("packages/core/dist/agent/ToolExecutor.js"), AnchoredSystemPathBuf("packages/core/dist/features/index.js"), AnchoredSystemPathBuf("packages/core/dist/persistance/index.d.ts"), AnchoredSystemPathBuf("packages/core/dist/persistance/IEmbeddingStore.d.ts"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoDataStore.js"), AnchoredSystemPathBuf("packages/core/dist/real-time/ChangeStreamManager.js"), AnchoredSystemPathBuf("packages/core/dist/agent/index.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/agent/ToolExecutor.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/schemas/index.d.ts"), AnchoredSystemPathBuf("packages/core/dist/real-time/index.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/agent/AgentStateManager.d.ts"), AnchoredSystemPathBuf("packages/core/dist/persistance/IDataStore.d.ts"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoEmbeddingProvider.d.ts"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoMemoryProvider.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/real-time"), AnchoredSystemPathBuf("packages/core/dist/index.js"), AnchoredSystemPathBuf("packages/core/dist/index.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/persistance/IMemoryStore.d.ts"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoDataStore.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/agent/AgentStateManager.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/features/hybridSearch.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/persistance/IMemoryStore.js"), AnchoredSystemPathBuf("packages/core/dist/persistance/index.js"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoConnection.d.ts"), AnchoredSystemPathBuf("packages/core/dist/agent/ToolExecutor.d.ts"), AnchoredSystemPathBuf("packages/core/dist/features/index.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoEmbeddingProvider.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/features"), AnchoredSystemPathBuf("packages/core/dist/schemas/index.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/index.d.ts"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoDataStore.d.ts"), AnchoredSystemPathBuf("packages/core/dist/agent/WorkflowEngine.js"), AnchoredSystemPathBuf("packages/core/dist/features/hybridSearch.js"), AnchoredSystemPathBuf("packages/core/dist/persistance/IEmbeddingStore.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoMemoryProvider.d.ts"), AnchoredSystemPathBuf("packages/core/dist/real-time/ChangeStreamManager.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/schemas"), AnchoredSystemPathBuf("packages/core/dist/features/hybridSearch.d.ts"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoEmbeddingProvider.js"), AnchoredSystemPathBuf("packages/core/dist/real-time/ChangeStreamManager.d.ts"), AnchoredSystemPathBuf("packages/core/dist/real-time/index.d.ts"), AnchoredSystemPathBuf("packages/core/dist/schemas/index.js"), AnchoredSystemPathBuf("packages/core/dist/agent"), AnchoredSystemPathBuf("packages/core/dist/features/index.d.ts"), AnchoredSystemPathBuf("packages/core/dist/agent/WorkflowEngine.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoConnection.js"), AnchoredSystemPathBuf("packages/core/dist/persistance/MongoMemoryProvider.js"), AnchoredSystemPathBuf("packages/core/dist/agent/WorkflowEngine.d.ts"), AnchoredSystemPathBuf("packages/core/dist/real-time/index.js"), AnchoredSystemPathBuf("packages/core/dist/agent/index.js"), AnchoredSystemPathBuf("packages/core/dist/persistance/index.d.ts.map"), AnchoredSystemPathBuf("packages/core/dist/persistance/IMemoryStore.d.ts.map")}
2025-06-21T13:43:43.378049Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-06-21T13:43:43.386687Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-21T13:44:01.476510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/persistance/MongoDataStore.ts")}
2025-06-21T13:44:01.476570Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mongodb-ai/core"), path: AnchoredSystemPathBuf("packages/core") }}))

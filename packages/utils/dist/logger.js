"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
var Logger = /** @class */ (function () {
    function Logger(serviceName) {
        this.serviceName = serviceName;
    }
    Logger.prototype.log = function (level, message, context) {
        if (context === void 0) { context = {}; }
        var logEntry = __assign({ timestamp: new Date().toISOString(), level: level, service: this.serviceName, message: message }, context);
        console.log(JSON.stringify(logEntry));
    };
    Logger.prototype.debug = function (message, context) {
        this.log('DEBUG', message, context);
    };
    Logger.prototype.info = function (message, context) {
        this.log('INFO', message, context);
    };
    Logger.prototype.warn = function (message, context) {
        this.log('WARN', message, context);
    };
    Logger.prototype.error = function (message, error, context) {
        this.log('ERROR', message, __assign(__assign({}, context), { error: {
                message: error.message,
                stack: error.stack,
            } }));
    };
    return Logger;
}());
exports.logger = new Logger('AIAgentBoilerplate');

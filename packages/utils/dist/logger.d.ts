declare class Logger {
    private serviceName;
    constructor(serviceName: string);
    private log;
    debug(message: string, context?: object): void;
    info(message: string, context?: object): void;
    warn(message: string, context?: object): void;
    error(message: string, error: Error, context?: object): void;
}
export declare const logger: Logger;
export {};
//# sourceMappingURL=logger.d.ts.map
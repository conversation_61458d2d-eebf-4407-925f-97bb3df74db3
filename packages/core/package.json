{"name": "@mongodb-ai/core", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.build.json", "dev": "tsc -w -p tsconfig.build.json", "lint": "eslint .", "test": "jest"}, "dependencies": {"mongodb": "^6.5.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "pino": "^8.19.0", "uuid": "^9.0.1", "openai": "^4.28.4", "@anthropic-ai/sdk": "^0.20.1", "zod": "^3.22.4", "eventemitter3": "^5.0.1"}, "devDependencies": {"tsconfig": "*", "eslint-config-custom": "*"}}
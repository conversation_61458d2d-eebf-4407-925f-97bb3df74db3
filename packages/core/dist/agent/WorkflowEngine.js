"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowEngine = void 0;
var WorkflowEngine = /** @class */ (function () {
    function WorkflowEngine(workflowStore, agentStateManager, toolExecutor) {
        this.workflowStore = workflowStore;
        this.agentStateManager = agentStateManager;
        this.toolExecutor = toolExecutor;
    }
    WorkflowEngine.prototype.executeWorkflow = function (workflowId) {
        return __awaiter(this, void 0, void 0, function () {
            var workflow, _i, _a, step, toolInput, output, error_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.workflowStore.findById(workflowId)];
                    case 1:
                        workflow = _b.sent();
                        if (!workflow) {
                            throw new Error("Workflow with ID '".concat(workflowId, "' not found."));
                        }
                        return [4 /*yield*/, this.updateWorkflowStatus(workflowId, 'in_progress')];
                    case 2:
                        _b.sent();
                        _i = 0, _a = workflow.workflow_definition.steps;
                        _b.label = 3;
                    case 3:
                        if (!(_i < _a.length)) return [3 /*break*/, 10];
                        step = _a[_i];
                        _b.label = 4;
                    case 4:
                        _b.trys.push([4, 7, , 9]);
                        // In a real system, we would handle depends_on, but for V1 we execute sequentially.
                        console.log("Executing step: ".concat(step.description));
                        toolInput = workflow.shared_context[step.input_mapping.source];
                        return [4 /*yield*/, this.toolExecutor.execute(step.tool_id, toolInput)];
                    case 5:
                        output = _b.sent();
                        // Update shared context
                        workflow.shared_context[step.step_id] = output;
                        return [4 /*yield*/, this.updateWorkflowContext(workflowId, workflow.shared_context)];
                    case 6:
                        _b.sent();
                        console.log("Step ".concat(step.step_id, " completed successfully."));
                        return [3 /*break*/, 9];
                    case 7:
                        error_1 = _b.sent();
                        console.error("Step ".concat(step.step_id, " failed:"), error_1);
                        return [4 /*yield*/, this.updateWorkflowStatus(workflowId, 'failed')];
                    case 8:
                        _b.sent();
                        return [2 /*return*/];
                    case 9:
                        _i++;
                        return [3 /*break*/, 3];
                    case 10: return [4 /*yield*/, this.updateWorkflowStatus(workflowId, 'completed')];
                    case 11:
                        _b.sent();
                        console.log("Workflow ".concat(workflowId, " completed successfully."));
                        return [2 /*return*/];
                }
            });
        });
    };
    WorkflowEngine.prototype.updateWorkflowStatus = function (workflowId, status) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.workflowStore.update(workflowId, { status: status })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    WorkflowEngine.prototype.updateWorkflowContext = function (workflowId, context) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.workflowStore.update(workflowId, { shared_context: context })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    return WorkflowEngine;
}());
exports.WorkflowEngine = WorkflowEngine;

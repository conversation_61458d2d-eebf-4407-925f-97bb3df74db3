import { Db } from 'mongodb';
import { IMemoryStore, ChatMessage } from './IMemoryStore';
export declare class MongoMemoryProvider implements IMemoryStore {
    private collection;
    constructor(db: Db);
    getHistory(agentId: string, sessionId: string): Promise<ChatMessage[]>;
    addMessage(agentId: string, sessionId: string, message: ChatMessage): Promise<void>;
}
//# sourceMappingURL=MongoMemoryProvider.d.ts.map